// authRoutes.js
import express from "express";
import passport from "passport";
import {
  registerUser,
  loginUser,
  verifyEmail,
  forgotPassword,
  resetPassword,
  getCurrentUser,
  logoutUser,
  googleAuthSuccess,
  googleAuthFailure,
} from "../controllers/auth.controller.js";
import { protect } from "../middlewares/auth.middleware.js";
import {
  registerValidator,
  loginValidator,
  forgotPasswordValidator,
  resetPasswordValidator,
  verifyEmailValidator
} from "../validators/auth.validator.js";
import { validateRequest } from "../middlewares/validateRequest.middleware.js";

const router = express.Router();

// Email Authentication Routes
router.post("/register", registerValidator, validateRequest, registerUser);
router.post("/login", loginValidator, validateRequest, loginUser);
router.post("/verify-email", verifyEmailValidator, validateRequest, verifyEmail);
router.post("/forgot-password", forgotPasswordValidator, validateRequest, forgotPassword);
router.post("/reset-password", resetPasswordValidator, validateRequest, resetPassword);

// Google OAuth Routes
router.get("/google", passport.authenticate("google", { scope: ["profile", "email"] }));
router.get("/google/callback",
  passport.authenticate("google", { failureRedirect: "/api/auth/google/failure" }),
  googleAuthSuccess
);
router.get("/google/success", googleAuthSuccess);
router.get("/google/failure", googleAuthFailure);

// Protected Routes
router.get("/me", protect, getCurrentUser);
router.post("/logout", protect, logoutUser);

export default router;
