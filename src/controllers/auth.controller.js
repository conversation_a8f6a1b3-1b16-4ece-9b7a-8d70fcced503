import User from "../models/user.model.js";
import { asyncHand<PERSON> } from "../utils/asyncHandler.js";
import logger from "../utils/logger.js";
import { ROLES } from "../utils/constants.js";
import bcrypt from "bcryptjs";
import crypto from "crypto";
import { generateToken, generateEmailVerificationToken, generatePasswordResetToken, verifyToken } from "../utils/jwt.js";
import { sendEmailVerification, sendPasswordReset, sendWelcomeEmail } from "../services/email.service.js";

// Register user with email and password
export const registerUser = asyncHandler(async (req, res) => {
  const { email, password, firstName, lastName, username } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({
    $or: [{ email }, { username }]
  });

  if (existingUser) {
    return res.status(400).json({
      success: false,
      message: existingUser.email === email
        ? "User with this email already exists"
        : "Username already taken"
    });
  }

  // Hash password
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(password, salt);

  // Generate email verification token
  const emailVerificationToken = generateEmailVerificationToken();

  // Create user
  const user = await User.create({
    email,
    password: hashedPassword,
    firstName,
    lastName,
    username,
    provider: "email",
    emailVerificationToken,
    isEmailVerified: false
  });

  // Send verification email
  try {
    await sendEmailVerification(email, emailVerificationToken);
  } catch (error) {
    logger.error("Failed to send verification email:", error);
    // Continue with registration even if email fails
  }

  // Generate JWT token
  const token = generateToken(user._id);

  res.status(201).json({
    success: true,
    message: "User registered successfully. Please check your email for verification.",
    data: {
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        isEmailVerified: user.isEmailVerified,
        role: user.role
      },
      token
    }
  });
});

// Login user with email and password
export const loginUser = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  // Check if user exists
  const user = await User.findOne({ email }).select("+password");

  if (!user) {
    return res.status(401).json({
      success: false,
      message: "Invalid email or password"
    });
  }

  // Check password
  const isPasswordValid = await bcrypt.compare(password, user.password);

  if (!isPasswordValid) {
    return res.status(401).json({
      success: false,
      message: "Invalid email or password"
    });
  }

  // Update last login
  user.lastLogin = new Date();
  await user.save();

  // Generate JWT token
  const token = generateToken(user._id);

  res.status(200).json({
    success: true,
    message: "Login successful",
    data: {
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        isEmailVerified: user.isEmailVerified,
        role: user.role
      },
      token
    }
  });
});

// Verify email
export const verifyEmail = asyncHandler(async (req, res) => {
  const { token } = req.body;

  try {
    // Verify the token
    const decoded = verifyToken(token);

    if (decoded.purpose !== "email_verification") {
      return res.status(400).json({
        success: false,
        message: "Invalid verification token"
      });
    }

    // Find user with this token
    const user = await User.findOne({ emailVerificationToken: token });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired verification token"
      });
    }

    // Update user
    user.isEmailVerified = true;
    user.emailVerificationToken = undefined;
    await user.save();

    // Send welcome email
    try {
      await sendWelcomeEmail(user.email, user.firstName);
    } catch (error) {
      logger.error("Failed to send welcome email:", error);
    }

    res.status(200).json({
      success: true,
      message: "Email verified successfully"
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: "Invalid or expired verification token"
    });
  }
});

// Forgot password
export const forgotPassword = asyncHandler(async (req, res) => {
  const { email } = req.body;

  const user = await User.findOne({ email });

  if (!user) {
    return res.status(404).json({
      success: false,
      message: "User not found with this email"
    });
  }

  // Generate reset token
  const resetToken = generatePasswordResetToken();

  // Save reset token and expiry
  user.passwordResetToken = resetToken;
  user.passwordResetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
  await user.save();

  // Send reset email
  try {
    await sendPasswordReset(email, resetToken);

    res.status(200).json({
      success: true,
      message: "Password reset email sent"
    });
  } catch (error) {
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save();

    logger.error("Failed to send password reset email:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to send password reset email"
    });
  }
});

// Reset password
export const resetPassword = asyncHandler(async (req, res) => {
  const { token, password } = req.body;

  try {
    // Verify the token
    const decoded = verifyToken(token);

    if (decoded.purpose !== "password_reset") {
      return res.status(400).json({
        success: false,
        message: "Invalid reset token"
      });
    }

    // Find user with this token
    const user = await User.findOne({
      passwordResetToken: token,
      passwordResetExpires: { $gt: new Date() }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired reset token"
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Update user
    user.password = hashedPassword;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save();

    res.status(200).json({
      success: true,
      message: "Password reset successfully"
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: "Invalid or expired reset token"
    });
  }
});

// Get current user
export const getCurrentUser = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user.id).populate('currentPlan');

  res.status(200).json({
    success: true,
    data: {
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        profileImage: user.profileImage,
        isEmailVerified: user.isEmailVerified,
        role: user.role,
        isPro: user.isPro,
        isLifetimePro: user.isLifetimePro,
        subscriptionSummary: user.getSubscriptionSummary(),
        currentPlan: user.currentPlan
      }
    }
  });
});

// Logout user
export const logoutUser = asyncHandler(async (req, res) => {
  res.cookie('token', '', {
    expires: new Date(0),
    httpOnly: true
  });

  res.status(200).json({
    success: true,
    message: "Logged out successfully"
  });
});
 